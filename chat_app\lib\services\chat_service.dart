import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/message.dart';
import '../models/chat.dart';
import 'encryption_service.dart';
import 'auth_service.dart';

class ChatService {
  static const String _messagesKey = 'messages';
  static const String _chatsKey = 'chats';

  final Uuid _uuid = const Uuid();
  final EncryptionService _encryptionService = EncryptionService();
  final AuthService _authService = AuthService();

  // Singleton pattern
  static final ChatService _instance = ChatService._internal();
  factory ChatService() => _instance;
  ChatService._internal();

  // Send a message
  Future<Message?> sendMessage(
    String receiverId,
    String content, {
    MessageType type = MessageType.text,
  }) async {
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) return null;

      final messageId = _uuid.v4();
      final now = DateTime.now();

      // Encrypt message content
      final encryptedContent = _encryptionService.encryptMessage(
        content,
        currentUser.id,
        receiverId,
      );

      final message = Message(
        id: messageId,
        senderId: currentUser.id,
        receiverId: receiverId,
        content: encryptedContent,
        type: type,
        status: MessageStatus.sent,
        timestamp: now,
        isEncrypted: true,
      );

      // Save message
      await _saveMessage(message);

      // Update or create chat
      await _updateChat(currentUser.id, receiverId, message);

      return message;
    } catch (e) {
      print('Error sending message: $e');
      return null;
    }
  }

  // Get messages for a specific chat
  Future<List<Message>> getMessages(String otherUserId) async {
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) return [];

      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getString(_messagesKey) ?? '[]';
      final messagesList = List<Map<String, dynamic>>.from(
        jsonDecode(messagesJson),
      );

      final messages =
          messagesList
              .map((json) => Message.fromJson(json))
              .where(
                (message) =>
                    (message.senderId == currentUser.id &&
                        message.receiverId == otherUserId) ||
                    (message.senderId == otherUserId &&
                        message.receiverId == currentUser.id),
              )
              .toList();

      // Sort by timestamp
      messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      // Decrypt messages
      final decryptedMessages =
          messages.map((message) {
            final decryptedContent = _encryptionService.decryptMessage(
              message.content,
              message.senderId,
              message.receiverId,
            );
            return message.copyWith(content: decryptedContent);
          }).toList();

      return decryptedMessages;
    } catch (e) {
      print('Error getting messages: $e');
      return [];
    }
  }

  // Get all chats for current user
  Future<List<Chat>> getChats() async {
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) return [];

      final prefs = await SharedPreferences.getInstance();
      final chatsJson = prefs.getString(_chatsKey) ?? '[]';
      final chatsList = List<Map<String, dynamic>>.from(jsonDecode(chatsJson));

      final chats =
          chatsList
              .map((json) => Chat.fromJson(json))
              .where((chat) => chat.participantIds.contains(currentUser.id))
              .toList();

      // Sort by last activity
      chats.sort((a, b) => b.lastActivity.compareTo(a.lastActivity));

      // Decrypt last messages
      final decryptedChats =
          chats.map((chat) {
            if (chat.lastMessage != null) {
              final decryptedContent = _encryptionService.decryptMessage(
                chat.lastMessage!.content,
                chat.lastMessage!.senderId,
                chat.lastMessage!.receiverId,
              );
              final decryptedMessage = chat.lastMessage!.copyWith(
                content: decryptedContent,
              );
              return chat.copyWith(lastMessage: decryptedMessage);
            }
            return chat;
          }).toList();

      return decryptedChats;
    } catch (e) {
      print('Error getting chats: $e');
      return [];
    }
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(String otherUserId) async {
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) return;

      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getString(_messagesKey) ?? '[]';
      final messagesList = List<Map<String, dynamic>>.from(
        jsonDecode(messagesJson),
      );

      // Update message status to read
      for (int i = 0; i < messagesList.length; i++) {
        final message = Message.fromJson(messagesList[i]);
        if (message.senderId == otherUserId &&
            message.receiverId == currentUser.id &&
            message.status != MessageStatus.read) {
          messagesList[i] =
              message.copyWith(status: MessageStatus.read).toJson();
        }
      }

      await prefs.setString(_messagesKey, jsonEncode(messagesList));

      // Update chat unread count
      await _updateChatUnreadCount(currentUser.id, otherUserId, 0);
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }

  // Private method to save message
  Future<void> _saveMessage(Message message) async {
    final prefs = await SharedPreferences.getInstance();
    final messagesJson = prefs.getString(_messagesKey) ?? '[]';
    final messagesList = List<Map<String, dynamic>>.from(
      jsonDecode(messagesJson),
    );

    messagesList.add(message.toJson());
    await prefs.setString(_messagesKey, jsonEncode(messagesList));
  }

  // Private method to update or create chat
  Future<void> _updateChat(
    String currentUserId,
    String otherUserId,
    Message lastMessage,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final chatsJson = prefs.getString(_chatsKey) ?? '[]';
    final chatsList = List<Map<String, dynamic>>.from(jsonDecode(chatsJson));

    final chatId = _generateChatId(currentUserId, otherUserId);

    // Find existing chat
    int chatIndex = chatsList.indexWhere((chatJson) {
      final chat = Chat.fromJson(chatJson);
      return chat.id == chatId;
    });

    if (chatIndex != -1) {
      // Update existing chat
      final existingChat = Chat.fromJson(chatsList[chatIndex]);
      final updatedChat = existingChat.copyWith(
        lastMessage: lastMessage,
        lastActivity: lastMessage.timestamp,
      );
      chatsList[chatIndex] = updatedChat.toJson();
    } else {
      // Create new chat
      final newChat = Chat(
        id: chatId,
        participantIds: [currentUserId, otherUserId],
        lastMessage: lastMessage,
        lastActivity: lastMessage.timestamp,
        unreadCount: lastMessage.senderId != currentUserId ? 1 : 0,
      );
      chatsList.add(newChat.toJson());
    }

    await prefs.setString(_chatsKey, jsonEncode(chatsList));
  }

  // Private method to update chat unread count
  Future<void> _updateChatUnreadCount(
    String currentUserId,
    String otherUserId,
    int count,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final chatsJson = prefs.getString(_chatsKey) ?? '[]';
    final chatsList = List<Map<String, dynamic>>.from(jsonDecode(chatsJson));

    final chatId = _generateChatId(currentUserId, otherUserId);

    final chatIndex = chatsList.indexWhere((chatJson) {
      final chat = Chat.fromJson(chatJson);
      return chat.id == chatId;
    });

    if (chatIndex != -1) {
      final existingChat = Chat.fromJson(chatsList[chatIndex]);
      final updatedChat = existingChat.copyWith(unreadCount: count);
      chatsList[chatIndex] = updatedChat.toJson();
      await prefs.setString(_chatsKey, jsonEncode(chatsList));
    }
  }

  // Generate consistent chat ID for two users
  String _generateChatId(String userId1, String userId2) {
    final users = [userId1, userId2]..sort();
    return 'chat_${users[0]}_${users[1]}';
  }

  // Delete a chat
  Future<void> deleteChat(String otherUserId) async {
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) return;

      final chatId = _generateChatId(currentUser.id, otherUserId);

      final prefs = await SharedPreferences.getInstance();

      // Remove chat
      final chatsJson = prefs.getString(_chatsKey) ?? '[]';
      final chatsList = List<Map<String, dynamic>>.from(jsonDecode(chatsJson));
      chatsList.removeWhere((chatJson) => Chat.fromJson(chatJson).id == chatId);
      await prefs.setString(_chatsKey, jsonEncode(chatsList));

      // Remove messages
      final messagesJson = prefs.getString(_messagesKey) ?? '[]';
      final messagesList = List<Map<String, dynamic>>.from(
        jsonDecode(messagesJson),
      );
      messagesList.removeWhere((messageJson) {
        final message = Message.fromJson(messageJson);
        return (message.senderId == currentUser.id &&
                message.receiverId == otherUserId) ||
            (message.senderId == otherUserId &&
                message.receiverId == currentUser.id);
      });
      await prefs.setString(_messagesKey, jsonEncode(messagesList));
    } catch (e) {
      print('Error deleting chat: $e');
    }
  }
}
