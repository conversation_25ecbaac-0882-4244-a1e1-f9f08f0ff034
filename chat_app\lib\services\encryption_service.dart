import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

class EncryptionService {
  static const int _keyLength = 32; // 256 bits
  static const int _ivLength = 16; // 128 bits
  
  // Singleton pattern
  static final EncryptionService _instance = EncryptionService._internal();
  factory EncryptionService() => _instance;
  EncryptionService._internal();

  // Generate a random key for encryption
  Uint8List _generateRandomBytes(int length) {
    final random = Random.secure();
    return Uint8List.fromList(
      List<int>.generate(length, (i) => random.nextInt(256))
    );
  }

  // Simple XOR-based encryption (for demonstration - in production use AES)
  String _xorEncrypt(String plaintext, Uint8List key) {
    final plaintextBytes = utf8.encode(plaintext);
    final encryptedBytes = <int>[];
    
    for (int i = 0; i < plaintextBytes.length; i++) {
      encryptedBytes.add(plaintextBytes[i] ^ key[i % key.length]);
    }
    
    return base64.encode(encryptedBytes);
  }

  // Simple XOR-based decryption
  String _xorDecrypt(String ciphertext, Uint8List key) {
    final ciphertextBytes = base64.decode(ciphertext);
    final decryptedBytes = <int>[];
    
    for (int i = 0; i < ciphertextBytes.length; i++) {
      decryptedBytes.add(ciphertextBytes[i] ^ key[i % key.length]);
    }
    
    return utf8.decode(decryptedBytes);
  }

  // Generate encryption key from user credentials
  Uint8List _deriveKey(String userId1, String userId2) {
    // Create a consistent key regardless of user order
    final users = [userId1, userId2]..sort();
    final combined = users.join('|');
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    return Uint8List.fromList(digest.bytes);
  }

  // Encrypt message content
  String encryptMessage(String content, String senderId, String receiverId) {
    try {
      final key = _deriveKey(senderId, receiverId);
      return _xorEncrypt(content, key);
    } catch (e) {
      // If encryption fails, return original content (not recommended for production)
      return content;
    }
  }

  // Decrypt message content
  String decryptMessage(String encryptedContent, String senderId, String receiverId) {
    try {
      final key = _deriveKey(senderId, receiverId);
      return _xorDecrypt(encryptedContent, key);
    } catch (e) {
      // If decryption fails, return encrypted content
      return encryptedContent;
    }
  }

  // Hash function for message integrity
  String generateMessageHash(String content, String senderId, String receiverId, DateTime timestamp) {
    final combined = '$content|$senderId|$receiverId|${timestamp.millisecondsSinceEpoch}';
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Verify message integrity
  bool verifyMessageIntegrity(String content, String senderId, String receiverId, DateTime timestamp, String hash) {
    final expectedHash = generateMessageHash(content, senderId, receiverId, timestamp);
    return expectedHash == hash;
  }
}
