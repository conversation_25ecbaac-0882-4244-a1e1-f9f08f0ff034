import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/user.dart';

class AuthService {
  static const String _userKey = 'current_user';
  static const String _usersKey = 'registered_users';
  static const String _isLoggedInKey = 'is_logged_in';

  final Uuid _uuid = const Uuid();

  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // Hash password with salt for security
  String _hashPassword(String password, String salt) {
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Generate random salt
  String _generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  // Register new user
  Future<Map<String, dynamic>> register(
    String username,
    String password,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if username already exists
      final usersJson = prefs.getString(_usersKey) ?? '{}';
      final users = Map<String, dynamic>.from(jsonDecode(usersJson));

      if (users.containsKey(username.toLowerCase())) {
        return {'success': false, 'message': 'Username already exists'};
      }

      // Validate username and password
      if (username.length < 3) {
        return {
          'success': false,
          'message': 'Username must be at least 3 characters long',
        };
      }

      if (password.length < 6) {
        return {
          'success': false,
          'message': 'Password must be at least 6 characters long',
        };
      }

      // Create new user
      final salt = _generateSalt();
      final hashedPassword = _hashPassword(password, salt);
      final userId = _uuid.v4();

      final user = User(
        id: userId,
        username: username,
        lastSeen: DateTime.now(),
        createdAt: DateTime.now(),
        isOnline: true,
      );

      // Store user credentials
      users[username.toLowerCase()] = {
        'password': hashedPassword,
        'salt': salt,
        'user': user.toJson(),
      };

      await prefs.setString(_usersKey, jsonEncode(users));
      await prefs.setString(_userKey, jsonEncode(user.toJson()));
      await prefs.setBool(_isLoggedInKey, true);

      return {
        'success': true,
        'message': 'Registration successful',
        'user': user,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Registration failed: ${e.toString()}',
      };
    }
  }

  // Login user
  Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString(_usersKey) ?? '{}';
      final users = Map<String, dynamic>.from(jsonDecode(usersJson));

      final userKey = username.toLowerCase();
      if (!users.containsKey(userKey)) {
        return {'success': false, 'message': 'User not found'};
      }

      final userData = users[userKey];
      final storedHash = userData['password'];
      final salt = userData['salt'];
      final inputHash = _hashPassword(password, salt);

      if (storedHash != inputHash) {
        return {'success': false, 'message': 'Invalid password'};
      }

      // Update user online status
      final user = User.fromJson(
        userData['user'],
      ).copyWith(isOnline: true, lastSeen: DateTime.now());

      // Update stored user data
      users[userKey]['user'] = user.toJson();
      await prefs.setString(_usersKey, jsonEncode(users));
      await prefs.setString(_userKey, jsonEncode(user.toJson()));
      await prefs.setBool(_isLoggedInKey, true);

      return {'success': true, 'message': 'Login successful', 'user': user};
    } catch (e) {
      return {'success': false, 'message': 'Login failed: ${e.toString()}'};
    }
  }

  // Logout user
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();

    // Update user offline status
    final currentUser = await getCurrentUser();
    if (currentUser != null) {
      final usersJson = prefs.getString(_usersKey) ?? '{}';
      final users = Map<String, dynamic>.from(jsonDecode(usersJson));

      final userKey = currentUser.username.toLowerCase();
      if (users.containsKey(userKey)) {
        final updatedUser = currentUser.copyWith(
          isOnline: false,
          lastSeen: DateTime.now(),
        );
        users[userKey]['user'] = updatedUser.toJson();
        await prefs.setString(_usersKey, jsonEncode(users));
      }
    }

    await prefs.remove(_userKey);
    await prefs.setBool(_isLoggedInKey, false);
  }

  // Get current logged-in user
  Future<User?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_userKey);

    if (userJson != null) {
      return User.fromJson(jsonDecode(userJson));
    }
    return null;
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  // Get all registered users (for chat list)
  Future<List<User>> getAllUsers() async {
    final prefs = await SharedPreferences.getInstance();
    final usersJson = prefs.getString(_usersKey) ?? '{}';
    print('DEBUG: usersJson = $usersJson'); // Debug output
    final users = Map<String, dynamic>.from(jsonDecode(usersJson));
    print('DEBUG: users map = $users'); // Debug output

    final currentUser = await getCurrentUser();
    print('DEBUG: currentUser = ${currentUser?.username}'); // Debug output
    final userList = <User>[];

    for (final userData in users.values) {
      final user = User.fromJson(userData['user']);
      print(
        'DEBUG: Found user: ${user.username} (id: ${user.id})',
      ); // Debug output
      if (currentUser == null || user.id != currentUser.id) {
        userList.add(user);
        print('DEBUG: Added user to list: ${user.username}'); // Debug output
      }
    }

    print('DEBUG: Final userList length = ${userList.length}'); // Debug output
    return userList;
  }
}
