import 'message.dart';

class Chat {
  final String id;
  final List<String> participantIds;
  final Message? lastMessage;
  final DateTime lastActivity;
  final int unreadCount;
  final bool isGroup;
  final String? groupName;
  final String? groupImage;

  Chat({
    required this.id,
    required this.participantIds,
    this.lastMessage,
    required this.lastActivity,
    this.unreadCount = 0,
    this.isGroup = false,
    this.groupName,
    this.groupImage,
  });

  factory Chat.fromJson(Map<String, dynamic> json) {
    return Chat(
      id: json['id'],
      participantIds: List<String>.from(json['participantIds']),
      lastMessage:
          json['lastMessage'] != null
              ? Message.fromJson(json['lastMessage'])
              : null,
      lastActivity: DateTime.parse(json['lastActivity']),
      unreadCount: json['unreadCount'] ?? 0,
      isGroup: json['isGroup'] ?? false,
      groupName: json['groupName'],
      groupImage: json['groupImage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'participantIds': participantIds,
      'lastMessage': lastMessage?.toJson(),
      'lastActivity': lastActivity.toIso8601String(),
      'unreadCount': unreadCount,
      'isGroup': isGroup,
      'groupName': groupName,
      'groupImage': groupImage,
    };
  }

  Chat copyWith({
    String? id,
    List<String>? participantIds,
    Message? lastMessage,
    DateTime? lastActivity,
    int? unreadCount,
    bool? isGroup,
    String? groupName,
    String? groupImage,
  }) {
    return Chat(
      id: id ?? this.id,
      participantIds: participantIds ?? this.participantIds,
      lastMessage: lastMessage ?? this.lastMessage,
      lastActivity: lastActivity ?? this.lastActivity,
      unreadCount: unreadCount ?? this.unreadCount,
      isGroup: isGroup ?? this.isGroup,
      groupName: groupName ?? this.groupName,
      groupImage: groupImage ?? this.groupImage,
    );
  }

  String getOtherParticipantId(String currentUserId) {
    return participantIds.firstWhere((id) => id != currentUserId);
  }
}
