import 'package:flutter/foundation.dart';
import '../models/message.dart';
import '../models/chat.dart';
import '../services/chat_service.dart';

class ChatProvider with ChangeNotifier {
  final ChatService _chatService = ChatService();

  List<Chat> _chats = [];
  List<Message> _currentMessages = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _currentChatUserId;

  List<Chat> get chats => _chats;
  List<Message> get currentMessages => _currentMessages;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get currentChatUserId => _currentChatUserId;

  // Load all chats
  Future<void> loadChats() async {
    _setLoading(true);
    try {
      _chats = await _chatService.getChats();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load chats: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load messages for a specific chat
  Future<void> loadMessages(String otherUserId) async {
    _setLoading(true);
    _currentChatUserId = otherUserId;
    try {
      _currentMessages = await _chatService.getMessages(otherUserId);

      // Mark messages as read
      await _chatService.markMessagesAsRead(otherUserId);

      // Refresh chats to update unread count
      await loadChats();

      notifyListeners();
    } catch (e) {
      _setError('Failed to load messages: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Send a message
  Future<bool> sendMessage(
    String receiverId,
    String content, {
    MessageType type = MessageType.text,
  }) async {
    try {
      final message = await _chatService.sendMessage(
        receiverId,
        content,
        type: type,
      );

      if (message != null) {
        // If we're currently viewing this chat, add the message to current messages
        if (_currentChatUserId == receiverId) {
          _currentMessages.add(message);
        }

        // Refresh chats to update last message
        await loadChats();

        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to send message: ${e.toString()}');
      return false;
    }
  }

  // Delete a chat
  Future<void> deleteChat(String otherUserId) async {
    try {
      await _chatService.deleteChat(otherUserId);

      // Remove from local chats list
      _chats.removeWhere((chat) => chat.participantIds.contains(otherUserId));

      // Clear current messages if this was the active chat
      if (_currentChatUserId == otherUserId) {
        _currentMessages.clear();
        _currentChatUserId = null;
      }

      notifyListeners();
    } catch (e) {
      _setError('Failed to delete chat: ${e.toString()}');
    }
  }

  // Get chat with specific user
  Chat? getChatWithUser(String userId) {
    try {
      return _chats.firstWhere((chat) => chat.participantIds.contains(userId));
    } catch (e) {
      return null;
    }
  }

  // Get unread message count for a specific user
  int getUnreadCount(String userId) {
    final chat = getChatWithUser(userId);
    return chat?.unreadCount ?? 0;
  }

  // Clear current chat
  void clearCurrentChat() {
    _currentMessages.clear();
    _currentChatUserId = null;
    notifyListeners();
  }

  // Add a message to current chat (for real-time updates)
  void addMessageToCurrentChat(Message message) {
    if (_currentChatUserId != null &&
        (message.senderId == _currentChatUserId ||
            message.receiverId == _currentChatUserId)) {
      _currentMessages.add(message);
      notifyListeners();
    }
  }

  // Update message status
  void updateMessageStatus(String messageId, MessageStatus status) {
    final messageIndex = _currentMessages.indexWhere(
      (msg) => msg.id == messageId,
    );
    if (messageIndex != -1) {
      _currentMessages[messageIndex] = _currentMessages[messageIndex].copyWith(
        status: status,
      );
      notifyListeners();
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear error message
  void clearError() {
    _clearError();
  }

  // Refresh data
  Future<void> refresh() async {
    await loadChats();
    if (_currentChatUserId != null) {
      await loadMessages(_currentChatUserId!);
    }
  }
}
