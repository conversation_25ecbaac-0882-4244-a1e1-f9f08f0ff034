// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:chat_app/services/auth_service.dart';
import 'package:chat_app/services/encryption_service.dart';
import 'package:chat_app/services/chat_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('Authentication Service Tests', () {
    test('should hash passwords correctly', () {
      final authService = AuthService();
      // Test that the service can be instantiated
      expect(authService, isNotNull);
    });

    test('should validate username requirements', () async {
      final authService = AuthService();

      // Test short username
      final result1 = await authService.register('ab', 'password123');
      expect(result1['success'], false);
      expect(result1['message'], contains('at least 3 characters'));

      // Test short password
      final result2 = await authService.register('testuser', '123');
      expect(result2['success'], false);
      expect(result2['message'], contains('at least 6 characters'));
    });
  });

  group('Encryption Service Tests', () {
    test('should encrypt and decrypt messages correctly', () {
      final encryptionService = EncryptionService();
      const message = 'Hello, this is a test message!';
      const userId1 = 'user1';
      const userId2 = 'user2';

      // Encrypt message
      final encrypted = encryptionService.encryptMessage(
        message,
        userId1,
        userId2,
      );
      expect(encrypted, isNotEmpty);
      expect(encrypted, isNot(equals(message)));

      // Decrypt message
      final decrypted = encryptionService.decryptMessage(
        encrypted,
        userId1,
        userId2,
      );
      expect(decrypted, equals(message));
    });

    test('should generate consistent keys for same user pairs', () {
      final encryptionService = EncryptionService();
      const message = 'Test message';
      const userId1 = 'user1';
      const userId2 = 'user2';

      // Encrypt with users in different order
      final encrypted1 = encryptionService.encryptMessage(
        message,
        userId1,
        userId2,
      );
      final encrypted2 = encryptionService.encryptMessage(
        message,
        userId2,
        userId1,
      );

      // Both should decrypt to the same message
      final decrypted1 = encryptionService.decryptMessage(
        encrypted1,
        userId1,
        userId2,
      );
      final decrypted2 = encryptionService.decryptMessage(
        encrypted2,
        userId1,
        userId2,
      );

      expect(decrypted1, equals(message));
      expect(decrypted2, equals(message));
    });
  });

  group('Chat Service Tests', () {
    test('should instantiate chat service', () {
      final chatService = ChatService();
      expect(chatService, isNotNull);
    });
  });
}
